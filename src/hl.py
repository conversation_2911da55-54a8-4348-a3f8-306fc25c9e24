import requests

def test_hl_request():
    url = "https://api.hyperliquid.xyz/info"
    response = requests.post(url, json={
        'type': 'metaAndAssetCtxs'
    })
    return response.json()

def test_raw():
    data = test_hl_request()
    universe_list = data[0]['universe']
    contexts_list = data[1]
    for info, context in zip(universe_list, contexts_list):
        print(info['name'], context)

if __name__ == "__main__":
    test_raw()
