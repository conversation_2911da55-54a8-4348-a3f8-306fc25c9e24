import asyncio
import logging
from typing import List, Dict, Any, Optional

from blofin.websocket_client import BlofinWsPublicClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("funding_rate_feed")

class FundingRateSubscriber:
    """Modular funding rate subscriber for Blofin WebSocket."""

    def __init__(self, instruments: List[str], is_demo: bool = False):
        """
        Initialize the funding rate subscriber.

        Args:
            instruments: List of instrument IDs to subscribe to (e.g., ["BTC-USDT", "ETH-USDT"])
            is_demo: Whether to use demo trading endpoints
        """
        self.instruments = instruments
        self.client = BlofinWsPublicClient(isDemo=is_demo)
        self.is_connected = False
        self.funding_rate_data: Dict[str, Dict[str, Any]] = {}

    async def connect(self) -> bool:
        """
        Connect to the WebSocket.

        Returns:
            bool: True if connection successful
        """
        try:
            success = await self.client.connect()
            if success:
                self.is_connected = True
                logger.info("Successfully connected to Blofin WebSocket")
            return success
        except Exception as e:
            logger.error(f"Failed to connect: {e}")
            return False

    async def subscribe_funding_rates(self) -> bool:
        """
        Subscribe to funding rates for all configured instruments.

        Returns:
            bool: True if all subscriptions successful
        """
        if not self.is_connected:
            logger.error("Not connected to WebSocket")
            return False

        success_count = 0
        for instrument in self.instruments:
            try:
                success = await self.client.subscribeFundingRate(instrument)
                if success:
                    success_count += 1
                    logger.info(f"Subscribed to funding rate for {instrument}")
                else:
                    logger.error(f"Failed to subscribe to funding rate for {instrument}")
            except Exception as e:
                logger.error(f"Error subscribing to {instrument}: {e}")

        return success_count == len(self.instruments)

    def process_funding_rate_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process a funding rate message and extract relevant data.

        Args:
            message: Raw WebSocket message

        Returns:
            Processed funding rate data or None if not a funding rate message
        """
        try:
            # Check if this is a funding rate message
            arg = message.get("arg", {})
            if arg.get("channel") != "funding-rate":
                return None

            data_list = message.get("data", [])
            if not data_list:
                return None

            # Process each funding rate update
            processed_data = []
            for data in data_list:
                inst_id = data.get("instId")
                funding_rate = data.get("fundingRate")
                funding_time = data.get("fundingTime")

                if inst_id and funding_rate is not None:
                    processed_item = {
                        "instrument": inst_id,
                        "funding_rate": float(funding_rate),
                        "funding_time": int(funding_time) if funding_time else None,
                        "timestamp": data.get("ts"),
                        "raw_data": data
                    }
                    processed_data.append(processed_item)

                    # Store latest data
                    self.funding_rate_data[inst_id] = processed_item

                    logger.info(f"Funding rate update - {inst_id}: {funding_rate}")

            return {
                "channel": "funding-rate",
                "updates": processed_data,
                "message_timestamp": message.get("ts")
            }

        except Exception as e:
            logger.error(f"Error processing funding rate message: {e}")
            return None

    async def listen_for_funding_rates(self, callback=None):
        """
        Listen for funding rate updates.

        Args:
            callback: Optional callback function to handle processed funding rate data
        """
        if not self.is_connected:
            logger.error("Not connected to WebSocket")
            return

        logger.info("Starting to listen for funding rate updates...")

        try:
            async for message in self.client.listen():
                processed_data = self.process_funding_rate_message(message)

                if processed_data:
                    if callback:
                        try:
                            await callback(processed_data)
                        except Exception as e:
                            logger.error(f"Error in callback: {e}")
                    else:
                        # Default handling - just log the data
                        for update in processed_data["updates"]:
                            logger.info(f"Funding Rate - {update['instrument']}: {update['funding_rate']:.8f}")

        except Exception as e:
            logger.error(f"Error listening for messages: {e}")

    def get_latest_funding_rate(self, instrument: str) -> Optional[Dict[str, Any]]:
        """
        Get the latest funding rate data for an instrument.

        Args:
            instrument: Instrument ID

        Returns:
            Latest funding rate data or None if not available
        """
        return self.funding_rate_data.get(instrument)

    def get_all_funding_rates(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all latest funding rate data.

        Returns:
            Dictionary of all funding rate data
        """
        return self.funding_rate_data.copy()

    async def close(self):
        """Close the WebSocket connection."""
        try:
            await self.client.close()
            self.is_connected = False
            logger.info("WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error closing connection: {e}")


async def custom_funding_rate_handler(data: Dict[str, Any]):
    """
    Custom callback function to handle funding rate updates.

    Args:
        data: Processed funding rate data
    """
    print(f"\n=== Funding Rate Update ===")
    print(f"Channel: {data['channel']}")
    print(f"Message Timestamp: {data.get('message_timestamp')}")

    for update in data["updates"]:
        print(f"Instrument: {update['instrument']}")
        print(f"Funding Rate: {update['funding_rate']:.8f}")
        print(f"Funding Time: {update['funding_time']}")
        print(f"Timestamp: {update['timestamp']}")
        print("---")


async def main():
    """Main function to demonstrate funding rate subscription."""
    # Define instruments to subscribe to
    instruments = ["BTC-USDT", "ETH-USDT", "SOL-USDT"]

    # Create subscriber
    subscriber = FundingRateSubscriber(instruments, is_demo=False)

    try:
        # Connect to WebSocket
        if not await subscriber.connect():
            logger.error("Failed to connect to WebSocket")
            return

        # Subscribe to funding rates
        if not await subscriber.subscribe_funding_rates():
            logger.error("Failed to subscribe to all funding rates")
            return

        # Listen for updates with custom handler
        await subscriber.listen_for_funding_rates(callback=custom_funding_rate_handler)

    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Error in main: {e}")
    finally:
        await subscriber.close()


if __name__ == "__main__":
    asyncio.run(main())
